from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.adapters.hubspot.client import HubSpotClient
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import (
    HubSpotCredentials,
    HubSpotTokenResponse,
    OrgEnvironment,
)
from app.workspace.services.base.oauth_connection import BaseOAuthConnection


class HubSpotConnectionService(
    BaseOAuthConnection[HubSpotCredentials, HubSpotTokenResponse]
):
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,
    ):
        super().__init__(
            db_session=db_session,
            integration_user_repo=integration_user_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=auth_url,
            token_url=token_url,
            redirect_uri=redirect_uri,
            flow_type=flow_type,
        )

    @property
    def integration_source(self) -> IntegrationSource:
        return IntegrationSource.HUBSPOT

    @property
    def default_scope(self) -> str:
        return "crm.objects.contacts.read crm.objects.companies.read crm.objects.deals.read crm.objects.leads.read crm.objects.custom.read crm.objects.users.read"

    @property
    def default_token_expiry_seconds(self) -> int:
        return 1800  # 30 minutes for HubSpot tokens

    async def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, HubSpotCredentials]:
        integration_config = await self.integration_cfg_repo.get_by_org_and_source(
            org_id=environment.organization_id, source=self.integration_source
        )

        if not integration_config:
            raise IntegrationConfigError(
                f"No HubSpot integration config found for organization {environment.organization_id}"
            )

        return integration_config, HubSpotCredentials.model_validate(
            integration_config.credentials
        )

    def _validate_credentials(self, credentials: HubSpotCredentials) -> None:
        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing HubSpot client_id and client_secret"
            )

    async def _extract_user_info_from_token(
        self, token_data: dict, _: HubSpotCredentials
    ) -> dict[str, str]:
        print("-" * 80)
        print("token_data", token_data)
        print("-" * 80)

        hubspot_client = HubSpotClient(
            access_token=token_data.get("access_token"),
        )

        user_info = await hubspot_client.get_user_info()

        print("-" * 80)
        print("user_info", user_info)
        print("-" * 80)

        return {
            "external_user_id": str(user_info.vid),
            "external_org_id": str(user_info.hub_id),
        }

    def _get_redirect_uri_for_token_exchange(self) -> str:
        return self.redirect_uri

    def _create_token_response(
        self, integration_user, expires_at: datetime
    ) -> HubSpotTokenResponse:
        return HubSpotTokenResponse(
            external_user_id=integration_user.external_user_id,
            external_org_id=integration_user.external_org_id,
            access_token=integration_user.access_token,
            expires_at=expires_at,
        )
