from enum import Enum
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.adapters.hubspot.refreshable_client_mixin import (
    HubSpotRefreshableClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics

logger = get_logger()


class HubSpotObjectType(str, Enum):
    COMPANY = "company"
    DEAL = "deal"
    CONTACT = "contact"
    TASK = "task"
    MEETING = "meeting"


class HubSpotHandler(HubSpotRefreshableClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_hubspot_client(credentials)

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.DEAL.value, opportunity_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        # Create filter for deals associated with the company
        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        # Convert offset to after parameter for HubSpot pagination
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["dealname", "dealstage"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                elif field.lower() in ["amount", "closedate"]:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.COMPANY.value, account_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["name", "domain"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.COMPANY.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.CONTACT.value, contact_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_contacts_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        # Create filter for contacts associated with the company
        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["firstname", "lastname", "email"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    # Task methods
    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.TASK.value, task_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    # Event methods (using meetings in HubSpot)
    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.MEETING.value, event_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    # Required CRM methods
    @HubSpotRefreshableClientMixin.handle_expired_session
    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        """
        Resolve account access for a HubSpot user.
        Note: HubSpot doesn't have the same user-based access model as Salesforce,
        so this is a simplified implementation.
        """
        logger.info(f"Resolving account access for HubSpot user: {crm_user_id}")

        # In HubSpot, we'll return all companies the user has access to
        # This is a simplified approach since HubSpot's permission model is different
        try:
            result = await self.hubspot_client.list_objects(
                object_type=HubSpotObjectType.COMPANY.value,
                properties=["name", "domain", "hs_object_id"],
                limit=1000,  # Get a large number of companies
            )

            account_access_data = []
            for company in result.get("results", []):
                properties = company.get("properties", {})
                account_access_data.append(
                    CRMAccountAccessData(
                        account_id=company.get("id", ""),
                        account_name=properties.get("name", ""),
                        user_id=crm_user_id,
                        access_level="read_write",  # Simplified access level
                    )
                )

            return account_access_data
        except Exception:
            logger.exception(f"Failed to resolve account access for user {crm_user_id}")
            return []

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_metrics(
        self, crm_user_id: str, field_mapping: dict[str, Any] | None = None
    ) -> CRMMetrics:
        """
        Get CRM metrics for a HubSpot user.
        """
        logger.info(f"Getting metrics for HubSpot user: {crm_user_id}")

        try:
            # Get deals count
            deals_result = await self.hubspot_client.list_objects(
                object_type=HubSpotObjectType.DEAL.value,
                properties=["dealstage", "amount"],
                limit=1000,
            )

            # Get companies count
            companies_result = await self.hubspot_client.list_objects(
                object_type=HubSpotObjectType.COMPANY.value,
                properties=["name"],
                limit=1000,
            )

            # Get contacts count
            contacts_result = await self.hubspot_client.list_objects(
                object_type=HubSpotObjectType.CONTACT.value,
                properties=["firstname", "lastname"],
                limit=1000,
            )

            deals = deals_result.get("results", [])
            total_deals = len(deals)

            # Calculate total deal value
            total_deal_value = 0
            for deal in deals:
                amount = deal.get("properties", {}).get("amount")
                if amount:
                    try:
                        total_deal_value += float(amount)
                    except (ValueError, TypeError):
                        pass

            return CRMMetrics(
                total_accounts=len(companies_result.get("results", [])),
                total_opportunities=total_deals,
                total_contacts=len(contacts_result.get("results", [])),
                total_opportunity_value=total_deal_value,
                user_id=crm_user_id,
            )
        except Exception:
            logger.exception(f"Failed to get metrics for user {crm_user_id}")
            return CRMMetrics(
                total_accounts=0,
                total_opportunities=0,
                total_contacts=0,
                total_opportunity_value=0.0,
                user_id=crm_user_id,
            )
